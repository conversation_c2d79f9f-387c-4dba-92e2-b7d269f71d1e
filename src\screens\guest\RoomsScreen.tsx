import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  Text,
  Searchbar,
  Button,
  Menu,
  Divider,
  Chip,
  Surface,
  ActivityIndicator,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';

import { useRoomStore } from '../../store/roomStore';
import { colors, spacing, typography } from '../../constants';
import { RoomCard } from '../../components/cards/RoomCard';
import { formatPrice } from '../../utils/currency';
import type { Room, RoomFilter, RoomType, RoomStatus } from '../../types/database';
import type { GuestNavigationProp } from '../../navigation/GuestNavigator';

const ROOM_TYPES: { label: string; value: RoomType }[] = [
  { label: 'Standard', value: 'standard' },
  { label: 'Deluxe', value: 'deluxe' },
  { label: 'Suite', value: 'suite' },
  { label: 'Presidential', value: 'presidential' },
];

const PRICE_RANGES = [
  { label: 'Under KSh 15,000', min: 0, max: 15000 },
  { label: 'KSh 15,000 - KSh 25,000', min: 15000, max: 25000 },
  { label: 'KSh 25,000 - KSh 50,000', min: 25000, max: 50000 },
  { label: 'Above KSh 50,000', min: 50000, max: Infinity },
];

const OCCUPANCY_OPTIONS = [
  { label: '1 Guest', value: 1 },
  { label: '2 Guests', value: 2 },
  { label: '4 Guests', value: 4 },
  { label: '6+ Guests', value: 6 },
];

export const RoomsScreen = () => {
  const navigation = useNavigation<GuestNavigationProp>();
  const { 
    rooms, 
    loading, 
    error, 
    filters,
    fetchRooms, 
    setFilters,
    clearError 
  } = useRoomStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [filterMenuVisible, setFilterMenuVisible] = useState(false);
  const [sortMenuVisible, setSortMenuVisible] = useState(false);
  const [sortBy, setSortBy] = useState<'price_asc' | 'price_desc' | 'type'>('price_asc');

  // Filter and sort rooms
  const filteredAndSortedRooms = React.useMemo(() => {
    let filtered = rooms.filter(room => {
      const matchesSearch = room.room_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           room.room_type.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           room.description.toLowerCase().includes(searchQuery.toLowerCase());
      
      return matchesSearch && room.status === 'available';
    });

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price_asc':
          return a.price_per_night - b.price_per_night;
        case 'price_desc':
          return b.price_per_night - a.price_per_night;
        case 'type':
          return a.room_type.localeCompare(b.room_type);
        default:
          return 0;
      }
    });

    return filtered;
  }, [rooms, searchQuery, sortBy]);

  useEffect(() => {
    loadRooms();
  }, [filters]);

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error, [
        { text: 'Retry', onPress: loadRooms },
        { text: 'OK', onPress: clearError }
      ]);
    }
  }, [error]);

  const loadRooms = async () => {
    await fetchRooms({ ...filters, status: 'available' });
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadRooms();
    setRefreshing(false);
  };

  const applyFilter = (newFilters: Partial<RoomFilter>) => {
    setFilters({ ...filters, ...newFilters });
    setFilterMenuVisible(false);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchQuery('');
    setFilterMenuVisible(false);
  };

  const getActiveFiltersCount = () => {
    const filterKeys = Object.keys(filters).filter(key => 
      filters[key as keyof RoomFilter] !== undefined && 
      key !== 'status'
    );
    return filterKeys.length + (searchQuery ? 1 : 0);
  };

  const renderRoomItem = ({ item }: { item: Room }) => (
    <RoomCard
      room={item}
      onPress={() => navigation.navigate('RoomDetails', { roomId: item.id })}
      style={styles.roomCard}
    />
  );

  const renderFilterChips = () => {
    const chips = [];
    
    if (filters.room_type) {
      chips.push(
        <Chip
          key="type"
          mode="outlined"
          onClose={() => applyFilter({ room_type: undefined })}
          style={styles.filterChip}
        >
          {filters.room_type}
        </Chip>
      );
    }

    if (filters.min_price || filters.max_price) {
      const priceLabel = filters.min_price && filters.max_price
        ? `${formatPrice(filters.min_price, { showSymbol: false })} - ${formatPrice(filters.max_price, { showSymbol: false })} KSh`
        : filters.min_price
        ? `Above ${formatPrice(filters.min_price)}`
        : `Under ${formatPrice(filters.max_price || 0)}`;

      chips.push(
        <Chip
          key="price"
          mode="outlined"
          onClose={() => applyFilter({ min_price: undefined, max_price: undefined })}
          style={styles.filterChip}
        >
          {priceLabel}
        </Chip>
      );
    }

    if (filters.max_occupancy) {
      chips.push(
        <Chip
          key="occupancy"
          mode="outlined"
          onClose={() => applyFilter({ max_occupancy: undefined })}
          style={styles.filterChip}
        >
          {filters.max_occupancy}+ Guests
        </Chip>
      );
    }

    return chips;
  };

  const renderHeader = () => (
    <View style={styles.header}>
      {/* Search Bar */}
      <Searchbar
        placeholder="Search rooms..."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchBar}
        iconColor={colors.primary}
      />

      {/* Filter and Sort Controls */}
      <View style={styles.controls}>
        <Menu
          visible={filterMenuVisible}
          onDismiss={() => setFilterMenuVisible(false)}
          anchor={
            <Button
              mode="outlined"
              onPress={() => setFilterMenuVisible(true)}
              icon="filter-variant"
              style={styles.controlButton}
            >
              Filters {getActiveFiltersCount() > 0 && `(${getActiveFiltersCount()})`}
            </Button>
          }
        >
          <Text style={styles.menuTitle}>Room Type</Text>
          {ROOM_TYPES.map((type) => (
            <Menu.Item
              key={type.value}
              onPress={() => applyFilter({ room_type: type.value })}
              title={type.label}
              leadingIcon={filters.room_type === type.value ? "check" : undefined}
            />
          ))}
          
          <Divider />
          <Text style={styles.menuTitle}>Price Range</Text>
          {PRICE_RANGES.map((range, index) => (
            <Menu.Item
              key={index}
              onPress={() => applyFilter({ 
                min_price: range.min === 0 ? undefined : range.min,
                max_price: range.max === Infinity ? undefined : range.max
              })}
              title={range.label}
            />
          ))}
          
          <Divider />
          <Text style={styles.menuTitle}>Occupancy</Text>
          {OCCUPANCY_OPTIONS.map((option) => (
            <Menu.Item
              key={option.value}
              onPress={() => applyFilter({ max_occupancy: option.value })}
              title={option.label}
              leadingIcon={filters.max_occupancy === option.value ? "check" : undefined}
            />
          ))}
          
          <Divider />
          <Menu.Item
            onPress={clearFilters}
            title="Clear All Filters"
            leadingIcon="close"
          />
        </Menu>

        <Menu
          visible={sortMenuVisible}
          onDismiss={() => setSortMenuVisible(false)}
          anchor={
            <Button
              mode="outlined"
              onPress={() => setSortMenuVisible(true)}
              icon="sort"
              style={styles.controlButton}
            >
              Sort
            </Button>
          }
        >
          <Menu.Item
            onPress={() => { setSortBy('price_asc'); setSortMenuVisible(false); }}
            title="Price: Low to High"
            leadingIcon={sortBy === 'price_asc' ? "check" : undefined}
          />
          <Menu.Item
            onPress={() => { setSortBy('price_desc'); setSortMenuVisible(false); }}
            title="Price: High to Low"
            leadingIcon={sortBy === 'price_desc' ? "check" : undefined}
          />
          <Menu.Item
            onPress={() => { setSortBy('type'); setSortMenuVisible(false); }}
            title="Room Type"
            leadingIcon={sortBy === 'type' ? "check" : undefined}
          />
        </Menu>
      </View>

      {/* Active Filters */}
      {renderFilterChips().length > 0 && (
        <View style={styles.activeFilters}>
          <Text style={styles.activeFiltersTitle}>Active Filters:</Text>
          <View style={styles.filterChipsContainer}>
            {renderFilterChips()}
          </View>
        </View>
      )}

      {/* Results Count */}
      <Text style={styles.resultsCount}>
        {filteredAndSortedRooms.length} room{filteredAndSortedRooms.length !== 1 ? 's' : ''} available
      </Text>
    </View>
  );

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <MaterialIcons name="hotel" size={64} color={colors.textSecondary} />
      <Text style={styles.emptyTitle}>No rooms found</Text>
      <Text style={styles.emptyMessage}>
        Try adjusting your search criteria or filters
      </Text>
      <Button
        mode="outlined"
        onPress={clearFilters}
        style={styles.clearFiltersButton}
      >
        Clear Filters
      </Button>
    </View>
  );

  if (loading && rooms.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading rooms...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={filteredAndSortedRooms}
        renderItem={renderRoomItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmpty}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  loadingText: {
    ...typography.body,
    marginTop: spacing.md,
    color: colors.textSecondary,
  },
  listContent: {
    paddingBottom: spacing.lg,
  },
  header: {
    padding: spacing.md,
    backgroundColor: colors.surface,
  },
  searchBar: {
    marginBottom: spacing.md,
    backgroundColor: colors.background,
  },
  controls: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.md,
  },
  controlButton: {
    flex: 1,
  },
  menuTitle: {
    ...typography.subtitle,
    fontWeight: 'bold',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    color: colors.primary,
  },
  activeFilters: {
    marginBottom: spacing.md,
  },
  activeFiltersTitle: {
    ...typography.caption,
    color: colors.textSecondary,
    marginBottom: spacing.sm,
  },
  filterChipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  filterChip: {
    marginRight: spacing.sm,
    marginBottom: spacing.sm,
  },
  resultsCount: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  roomCard: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
  },
  emptyTitle: {
    ...typography.h3,
    fontWeight: 'bold',
    marginTop: spacing.md,
    marginBottom: spacing.sm,
    color: colors.textPrimary,
  },
  emptyMessage: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  clearFiltersButton: {
    marginTop: spacing.md,
  },
});
